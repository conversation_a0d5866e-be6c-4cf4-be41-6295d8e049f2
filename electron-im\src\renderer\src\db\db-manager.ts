// # 本地数据库相关 (IndexedDB)
// # 数据库管理，提供加密接口

import type { Message, ChatSession } from '../store/message'

// 数据库配置
const DB_NAME = 'ElectronIMDB'
const DB_VERSION = 1

// 对象存储名称
const STORES = {
  MESSAGES: 'messages',
  CHAT_SESSIONS: 'chatSessions',
  USER_SETTINGS: 'userSettings'
} as const

// 加密相关接口
interface EncryptedData {
  data: string // 加密后的数据
  iv: string // 初始化向量
}

// 数据库存储的消息格式
interface StoredMessage extends Omit<Message, 'content'> {
  content: EncryptedData // 加密的消息内容
  chatId: string // 聊天ID (用于索引)
  userId: string // 当前用户ID (用于多用户支持)
}

// 数据库存储的会话格式
interface StoredChatSession extends Omit<ChatSession, 'lastMessage'> {
  lastMessage?: StoredMessage
  userId: string // 当前用户ID
  chatUserId: string // 聊天对象的用户ID（与 ChatSession.userId 相同）
}

// 用户设置
interface UserSettings {
  userId: string
  encryptionKey?: string // 用户的加密密钥
  lastSyncTime?: number
}

/**
 * IndexedDB 数据库管理器
 * 提供消息的本地缓存、加密存储
 */
export class DatabaseManager {
  private db: IDBDatabase | null = null
  private encryptionKey: CryptoKey | null = null
  private currentUserId: string = ''

  /**
   * 初始化数据库
   */
  async initialize(userId: string): Promise<void> {
    this.currentUserId = userId

    try {
      // 打开数据库
      this.db = await this.openDatabase()

      // 初始化或获取加密密钥
      await this.initializeEncryption()

      console.log('✅ [DatabaseManager] 数据库初始化成功')
    } catch (error) {
      console.error('❌ [DatabaseManager] 数据库初始化失败:', error)
      throw error
    }
  }

  /**
   * 打开 IndexedDB 数据库
   */
  private openDatabase(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        reject(new Error(`数据库打开失败: ${request.error?.message}`))
      }

      request.onsuccess = () => {
        resolve(request.result)
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        this.createObjectStores(db)
      }
    })
  }

  /**
   * 创建对象存储
   */
  private createObjectStores(db: IDBDatabase): void {
    // 消息存储
    if (!db.objectStoreNames.contains(STORES.MESSAGES)) {
      const messageStore = db.createObjectStore(STORES.MESSAGES, { keyPath: 'id' })
      messageStore.createIndex('chatId', 'chatId', { unique: false })
      messageStore.createIndex('userId', 'userId', { unique: false })
      messageStore.createIndex('timestamp', 'timestamp', { unique: false })
      messageStore.createIndex('userChat', ['userId', 'chatId'], { unique: false })
    }

    // 聊天会话存储
    if (!db.objectStoreNames.contains(STORES.CHAT_SESSIONS)) {
      const sessionStore = db.createObjectStore(STORES.CHAT_SESSIONS, {
        keyPath: ['userId', 'chatUserId']
      })
      sessionStore.createIndex('userId', 'userId', { unique: false })
      sessionStore.createIndex('lastActiveTime', 'lastActiveTime', { unique: false })
    }

    // 用户设置存储
    if (!db.objectStoreNames.contains(STORES.USER_SETTINGS)) {
      db.createObjectStore(STORES.USER_SETTINGS, { keyPath: 'userId' })
    }
  }

  /**
   * 初始化加密
   */
  private async initializeEncryption(): Promise<void> {
    try {
      // 尝试从数据库获取现有的加密密钥
      const settings = await this.getUserSettings(this.currentUserId)

      if (settings?.encryptionKey) {
        // 从存储的密钥恢复 CryptoKey
        const keyData = JSON.parse(settings.encryptionKey)
        this.encryptionKey = await crypto.subtle.importKey(
          'raw',
          new Uint8Array(keyData),
          { name: 'AES-GCM' },
          false,
          ['encrypt', 'decrypt']
        )
      } else {
        // 生成新的加密密钥
        this.encryptionKey = await crypto.subtle.generateKey(
          { name: 'AES-GCM', length: 256 },
          true,
          ['encrypt', 'decrypt']
        )

        // 导出并保存密钥
        const exportedKey = await crypto.subtle.exportKey('raw', this.encryptionKey)
        const keyArray = Array.from(new Uint8Array(exportedKey))

        await this.saveUserSettings({
          userId: this.currentUserId,
          encryptionKey: JSON.stringify(keyArray),
          lastSyncTime: Date.now()
        })
      }

      console.log('✅ [DatabaseManager] 加密初始化成功')
    } catch (error) {
      console.error('❌ [DatabaseManager] 加密初始化失败:', error)
      throw error
    }
  }

  /**
   * 加密文本数据
   */
  private async encryptData(data: string): Promise<EncryptedData> {
    if (!this.encryptionKey) {
      throw new Error('加密密钥未初始化')
    }

    try {
      const encoder = new TextEncoder()
      const dataBuffer = encoder.encode(data)

      // 生成随机初始化向量
      const iv = crypto.getRandomValues(new Uint8Array(12))

      // 加密数据
      const encryptedBuffer = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        dataBuffer
      )

      // 转换为 base64 字符串
      const encryptedArray = new Uint8Array(encryptedBuffer)
      const encryptedBase64 = btoa(String.fromCharCode(...encryptedArray))
      const ivBase64 = btoa(String.fromCharCode(...iv))

      return {
        data: encryptedBase64,
        iv: ivBase64
      }
    } catch (error) {
      console.error('❌ [DatabaseManager] 数据加密失败:', error)
      throw error
    }
  }

  /**
   * 解密文本数据
   */
  private async decryptData(encryptedData: EncryptedData): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('加密密钥未初始化')
    }

    try {
      // 从 base64 转换回二进制
      const encryptedArray = new Uint8Array(
        atob(encryptedData.data)
          .split('')
          .map((char) => char.charCodeAt(0))
      )
      const iv = new Uint8Array(
        atob(encryptedData.iv)
          .split('')
          .map((char) => char.charCodeAt(0))
      )

      // 解密数据
      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        encryptedArray
      )

      // 转换为字符串
      const decoder = new TextDecoder()
      return decoder.decode(decryptedBuffer)
    } catch (error) {
      console.error('❌ [DatabaseManager] 数据解密失败:', error)
      throw error
    }
  }

  /**
   * 保存消息到数据库
   */
  async saveMessage(message: Message, chatId: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      // 加密消息内容
      const encryptedContent = await this.encryptData(message.content)

      const storedMessage: StoredMessage = {
        ...message,
        content: encryptedContent,
        chatId,
        userId: this.currentUserId
      }

      await new Promise<void>((resolve, reject) => {
        const transaction = this.db!.transaction([STORES.MESSAGES], 'readwrite')
        const store = transaction.objectStore(STORES.MESSAGES)

        transaction.oncomplete = () => {
          console.log(`✅ [DatabaseManager] 消息已保存: ${message.id}`)
          resolve()
        }

        transaction.onerror = () => {
          console.error('❌ [DatabaseManager] 保存消息事务失败:', transaction.error)
          reject(transaction.error)
        }

        const request = store.put(storedMessage)
        request.onerror = () => {
          console.error('❌ [DatabaseManager] 保存消息请求失败:', request.error)
          reject(request.error)
        }
      })
    } catch (error) {
      console.error('❌ [DatabaseManager] 保存消息失败:', error)
      throw error
    }
  }

  /**
   * 批量保存消息
   */
  async saveMessages(messages: Message[], chatId: string): Promise<void> {
    if (!this.db || messages.length === 0) {
      return
    }

    try {
      // 先预处理所有消息（加密）
      const storedMessages: StoredMessage[] = []
      for (const message of messages) {
        const encryptedContent = await this.encryptData(message.content)
        storedMessages.push({
          ...message,
          content: encryptedContent,
          chatId,
          userId: this.currentUserId
        })
      }

      // 在单个事务中批量保存
      await new Promise<void>((resolve, reject) => {
        const transaction = this.db!.transaction([STORES.MESSAGES], 'readwrite')
        const store = transaction.objectStore(STORES.MESSAGES)

        let completedCount = 0
        const totalCount = storedMessages.length

        transaction.oncomplete = () => {
          console.log(`✅ [DatabaseManager] 事务完成，批量保存消息: ${totalCount} 条`)
          resolve()
        }

        transaction.onerror = () => {
          console.error('❌ [DatabaseManager] 事务失败:', transaction.error)
          reject(transaction.error)
        }

        // 批量添加消息
        storedMessages.forEach((storedMessage) => {
          const request = store.put(storedMessage)
          request.onsuccess = () => {
            completedCount++
            if (completedCount === totalCount) {
              console.log(
                `✅ [DatabaseManager] 所有消息已添加到事务: ${completedCount}/${totalCount}`
              )
            }
          }
          request.onerror = () => {
            console.error('❌ [DatabaseManager] 保存单条消息失败:', request.error)
            reject(request.error)
          }
        })
      })
      console.log(`✅ [DatabaseManager] 批量保存消息完成: ${messages.length} 条`)
    } catch (error) {
      console.error('❌ [DatabaseManager] 批量保存消息失败:', error)
      throw error
    }
  }

  /**
   * 获取聊天消息
   */
  async getChatMessages(chatId: string, limit = 50, offset = 0): Promise<Message[]> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      const transaction = this.db.transaction([STORES.MESSAGES], 'readonly')
      const store = transaction.objectStore(STORES.MESSAGES)
      const index = store.index('userChat')

      // 先获取所有原始数据
      const storedMessages: StoredMessage[] = []

      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(
          IDBKeyRange.only([this.currentUserId, chatId]),
          'prev' // 按时间倒序
        )

        let count = 0
        let skipped = 0

        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result

          if (cursor && count < limit) {
            if (skipped < offset) {
              skipped++
              cursor.continue()
              return
            }

            storedMessages.push(cursor.value as StoredMessage)
            count++
            cursor.continue()
          } else {
            resolve()
          }
        }

        request.onerror = () => reject(request.error)
      })

      // 在事务外进行解密操作
      const messages: Message[] = []
      for (const storedMessage of storedMessages) {
        try {
          const decryptedContent = await this.decryptData(storedMessage.content)

          const message: Message = {
            ...storedMessage,
            content: decryptedContent
          }

          // 移除数据库特有的字段
          delete (message as any).chatId
          delete (message as any).userId

          messages.push(message)
        } catch (error) {
          console.error('❌ [DatabaseManager] 解密消息失败:', error)
          // 跳过解密失败的消息
        }
      }

      // 按时间正序返回
      return messages.reverse()
    } catch (error) {
      console.error('❌ [DatabaseManager] 获取聊天消息失败:', error)
      throw error
    }
  }

  /**
   * 获取最新的聊天消息
   */
  async getLatestMessage(chatId: string): Promise<Message | null> {
    const messages = await this.getChatMessages(chatId, 1, 0)
    return messages.length > 0 ? messages[0] : null
  }

  /**
   * 删除聊天消息
   */
  async deleteChatMessages(chatId: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      // 先获取所有要删除的消息键
      const transaction = this.db.transaction([STORES.MESSAGES], 'readonly')
      const store = transaction.objectStore(STORES.MESSAGES)
      const index = store.index('userChat')

      const keysToDelete: string[] = []

      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only([this.currentUserId, chatId]))

        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result
          if (cursor) {
            keysToDelete.push(cursor.primaryKey as string)
            cursor.continue()
          } else {
            resolve()
          }
        }

        request.onerror = () => reject(request.error)
      })

      // 在新的事务中删除消息
      if (keysToDelete.length > 0) {
        await new Promise<void>((resolve, reject) => {
          const deleteTransaction = this.db!.transaction([STORES.MESSAGES], 'readwrite')
          const deleteStore = deleteTransaction.objectStore(STORES.MESSAGES)

          deleteTransaction.oncomplete = () => resolve()
          deleteTransaction.onerror = () => reject(deleteTransaction.error)

          keysToDelete.forEach((key) => {
            const deleteRequest = deleteStore.delete(key)
            deleteRequest.onerror = () => reject(deleteRequest.error)
          })
        })
      }
    } catch (error) {
      console.error('❌ [DatabaseManager] 删除聊天消息失败:', error)
      throw error
    }
  }

  /**
   * 保存聊天会话
   */
  async saveChatSession(session: ChatSession): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      const storedSession: StoredChatSession = {
        ...session,
        userId: this.currentUserId,
        chatUserId: session.userId // 聊天对象的用户ID
      }

      // 如果有最后一条消息，需要加密
      if (session.lastMessage) {
        const encryptedContent = await this.encryptData(session.lastMessage.content)
        storedSession.lastMessage = {
          ...session.lastMessage,
          content: encryptedContent,
          chatId: session.userId,
          userId: this.currentUserId
        }
      }

      await new Promise<void>((resolve, reject) => {
        const transaction = this.db!.transaction([STORES.CHAT_SESSIONS], 'readwrite')
        const store = transaction.objectStore(STORES.CHAT_SESSIONS)

        transaction.oncomplete = () => {
          console.log(`✅ [DatabaseManager] 聊天会话已保存: ${session.userId}`)
          resolve()
        }

        transaction.onerror = () => {
          console.error('❌ [DatabaseManager] 保存会话事务失败:', transaction.error)
          reject(transaction.error)
        }

        const request = store.put(storedSession)
        request.onerror = () => {
          console.error('❌ [DatabaseManager] 保存会话请求失败:', request.error)
          reject(request.error)
        }
      })
    } catch (error) {
      console.error('❌ [DatabaseManager] 保存聊天会话失败:', error)
      throw error
    }
  }

  /**
   * 获取所有聊天会话
   */
  async getChatSessions(): Promise<ChatSession[]> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      const transaction = this.db.transaction([STORES.CHAT_SESSIONS], 'readonly')
      const store = transaction.objectStore(STORES.CHAT_SESSIONS)
      const index = store.index('userId')

      // 先获取所有原始数据
      const storedSessions: StoredChatSession[] = []

      await new Promise<void>((resolve, reject) => {
        const request = index.openCursor(IDBKeyRange.only(this.currentUserId))

        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result

          if (cursor) {
            storedSessions.push(cursor.value as StoredChatSession)
            cursor.continue()
          } else {
            resolve()
          }
        }

        request.onerror = () => reject(request.error)
      })

      // 在事务外进行解密操作
      const sessions: ChatSession[] = []
      for (const storedSession of storedSessions) {
        try {
          const session: ChatSession = {
            userId: storedSession.chatUserId, // 使用聊天对象的用户ID
            userName: storedSession.userName,
            userAvatar: storedSession.userAvatar,
            unreadCount: storedSession.unreadCount,
            lastActiveTime: storedSession.lastActiveTime
          }

          // 如果有最后一条消息，需要解密
          if (storedSession.lastMessage) {
            const decryptedContent = await this.decryptData(storedSession.lastMessage.content)
            session.lastMessage = {
              ...storedSession.lastMessage,
              content: decryptedContent
            }
            // 移除数据库特有的字段
            delete (session.lastMessage as any).chatId
            delete (session.lastMessage as any).userId
          }

          sessions.push(session)
        } catch (error) {
          console.error('❌ [DatabaseManager] 解密会话失败:', error)
          // 跳过解密失败的会话
        }
      }

      return sessions
    } catch (error) {
      console.error('❌ [DatabaseManager] 获取聊天会话失败:', error)
      throw error
    }
  }

  /**
   * 删除聊天会话
   */
  async deleteChatSession(sessionUserId: string): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      await new Promise<void>((resolve, reject) => {
        const transaction = this.db!.transaction([STORES.CHAT_SESSIONS], 'readwrite')
        const store = transaction.objectStore(STORES.CHAT_SESSIONS)

        transaction.oncomplete = () => {
          console.log(`✅ [DatabaseManager] 聊天会话已删除: ${sessionUserId}`)
          resolve()
        }

        transaction.onerror = () => {
          console.error('❌ [DatabaseManager] 删除会话事务失败:', transaction.error)
          reject(transaction.error)
        }

        const request = store.delete([this.currentUserId, sessionUserId])
        request.onerror = () => {
          console.error('❌ [DatabaseManager] 删除会话请求失败:', request.error)
          reject(request.error)
        }
      })

      console.log(`✅ [DatabaseManager] 聊天会话已删除: ${sessionUserId}`)
    } catch (error) {
      console.error('❌ [DatabaseManager] 删除聊天会话失败:', error)
      throw error
    }
  }

  /**
   * 保存用户设置（加密密钥）
   */
  private async saveUserSettings(settings: UserSettings): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      await new Promise<void>((resolve, reject) => {
        const transaction = this.db!.transaction([STORES.USER_SETTINGS], 'readwrite')
        const store = transaction.objectStore(STORES.USER_SETTINGS)

        transaction.oncomplete = () => resolve()
        transaction.onerror = () => reject(transaction.error)

        const request = store.put(settings)
        request.onerror = () => reject(request.error)
      })
    } catch (error) {
      console.error('❌ [DatabaseManager] 保存用户设置失败:', error)
      throw error
    }
  }

  /**
   * 获取用户设置
   */
  private async getUserSettings(userId: string): Promise<UserSettings | null> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      const transaction = this.db.transaction([STORES.USER_SETTINGS], 'readonly')
      const store = transaction.objectStore(STORES.USER_SETTINGS)

      return new Promise((resolve, reject) => {
        const request = store.get(userId)
        request.onsuccess = () => resolve(request.result || null)
        request.onerror = () => reject(request.error)
      })
    } catch (error) {
      console.error('❌ [DatabaseManager] 获取用户设置失败:', error)
      throw error
    }
  }

  /**
   * 清理数据库（删除当前用户的所有数据）
   */
  async clearUserData(): Promise<void> {
    if (!this.db) {
      return
    }

    try {
      const transaction = this.db.transaction(
        [STORES.MESSAGES, STORES.CHAT_SESSIONS, STORES.USER_SETTINGS],
        'readwrite'
      )

      // 清理消息
      const messageStore = transaction.objectStore(STORES.MESSAGES)
      const messageIndex = messageStore.index('userId')
      await this.clearStoreByIndex(messageIndex, this.currentUserId)

      // 清理会话
      const sessionStore = transaction.objectStore(STORES.CHAT_SESSIONS)
      const sessionIndex = sessionStore.index('userId')
      await this.clearStoreByIndex(sessionIndex, this.currentUserId)

      // 清理用户设置
      const settingsStore = transaction.objectStore(STORES.USER_SETTINGS)
      await new Promise<void>((resolve, reject) => {
        const request = settingsStore.delete(this.currentUserId)
        request.onsuccess = () => resolve()
        request.onerror = () => reject(request.error)
      })

      console.log('✅ [DatabaseManager] 用户数据已清理')
    } catch (error) {
      console.error('❌ [DatabaseManager] 清理用户数据失败:', error)
      throw error
    }
  }

  /**
   * 通过索引清理存储
   */
  private clearStoreByIndex(index: IDBIndex, keyValue: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // 先收集所有要删除的键
      const keysToDelete: any[] = []

      const request = index.openCursor(IDBKeyRange.only(keyValue))

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result
        if (cursor) {
          keysToDelete.push(cursor.primaryKey)
          cursor.continue()
        } else {
          // 在新的事务中删除数据
          const deleteTransaction = index.objectStore.transaction.db.transaction(
            [index.objectStore.name],
            'readwrite'
          )
          const deleteStore = deleteTransaction.objectStore(index.objectStore.name)

          deleteTransaction.oncomplete = () => resolve()
          deleteTransaction.onerror = () => reject(deleteTransaction.error)

          keysToDelete.forEach((key) => {
            const deleteRequest = deleteStore.delete(key)
            deleteRequest.onerror = () => reject(deleteRequest.error)
          })
        }
      }

      request.onerror = () => reject(request.error)
    })
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      this.encryptionKey = null
      console.log('✅ [DatabaseManager] 数据库连接已关闭')
    }
  }
}

// 导出数据库管理器实例
export const dbManager = new DatabaseManager()
