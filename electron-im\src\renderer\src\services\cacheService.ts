// 缓存服务 - 集成 IndexedDB 和内存存储
import { dbManager } from '../db/db-manager'
import type { Message, ChatSession } from '../store/message'

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  MEMORY_FIRST = 'memory_first', // 优先使用内存，数据库作为备份
  DATABASE_FIRST = 'database_first', // 优先使用数据库，内存作为缓存
  HYBRID = 'hybrid' // 混合模式，根据数据量自动选择
}

/**
 * 缓存配置
 */
interface CacheConfig {
  strategy: CacheStrategy
  maxMemoryMessages: number // 内存中最大消息数量
  autoSyncInterval: number // 自动同步间隔（毫秒）
  enableEncryption: boolean // 是否启用加密
}

/**
 * 缓存服务类
 * 负责协调内存存储和 IndexedDB 存储
 */
export class CacheService {
  private config: CacheConfig
  private isInitialized = false
  private currentUserId = ''
  private syncTimer: NodeJS.Timeout | null = null

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      strategy: CacheStrategy.HYBRID,
      maxMemoryMessages: 1000,
      autoSyncInterval: 30000, // 30秒
      enableEncryption: true,
      ...config
    }
  }

  /**
   * 初始化缓存服务
   */
  async initialize(userId: string): Promise<void> {
    if (this.isInitialized && this.currentUserId === userId) {
      return
    }

    try {
      this.currentUserId = userId

      // 初始化数据库管理器
      await dbManager.initialize(userId)

      // 启动自动同步
      this.startAutoSync()

      this.isInitialized = true
      console.log('✅ [CacheService] 缓存服务初始化成功')
    } catch (error) {
      console.error('❌ [CacheService] 缓存服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 缓存消息到本地数据库
   */
  async cacheMessage(message: Message, chatId: string): Promise<void> {
    if (!this.isInitialized) {
      console.warn('⚠️ [CacheService] 缓存服务未初始化，跳过消息缓存')
      return
    }

    try {
      await dbManager.saveMessage(message, chatId)
      console.log(`✅ [CacheService] 消息已缓存: ${message.id}`)
    } catch (error) {
      console.error('❌ [CacheService] 缓存消息失败:', error)
      // 不抛出错误，避免影响正常的消息流程
    }
  }

  /**
   * 批量缓存消息
   */
  async cacheMessages(messages: Message[], chatId: string): Promise<void> {
    if (!this.isInitialized || messages.length === 0) {
      return
    }

    try {
      await dbManager.saveMessages(messages, chatId)
      console.log(`✅ [CacheService] 批量缓存消息完成: ${messages.length} 条`)
    } catch (error) {
      console.error('❌ [CacheService] 批量缓存消息失败:', error)
    }
  }

  /**
   * 从缓存获取聊天消息
   */
  async getCachedMessages(chatId: string, limit = 50, offset = 0): Promise<Message[]> {
    if (!this.isInitialized) {
      return []
    }

    try {
      const messages = await dbManager.getChatMessages(chatId, limit, offset)
      console.log(`✅ [CacheService] 从缓存获取消息: ${messages.length} 条`)
      return messages
    } catch (error) {
      console.error('❌ [CacheService] 获取缓存消息失败:', error)
      return []
    }
  }

  /**
   * 获取最新的缓存消息
   */
  async getLatestCachedMessage(chatId: string): Promise<Message | null> {
    if (!this.isInitialized) {
      return null
    }

    try {
      return await dbManager.getLatestMessage(chatId)
    } catch (error) {
      console.error('❌ [CacheService] 获取最新缓存消息失败:', error)
      return null
    }
  }

  /**
   * 缓存聊天会话
   */
  async cacheChatSession(session: ChatSession): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      await dbManager.saveChatSession(session)
      console.log(`✅ [CacheService] 聊天会话已缓存: ${session.userId}`)
    } catch (error) {
      console.error('❌ [CacheService] 缓存聊天会话失败:', error)
    }
  }

  /**
   * 获取缓存的聊天会话
   */
  async getCachedChatSessions(): Promise<ChatSession[]> {
    if (!this.isInitialized) {
      return []
    }

    try {
      const sessions = await dbManager.getChatSessions()
      console.log(`✅ [CacheService] 从缓存获取聊天会话: ${sessions.length} 个`)
      return sessions
    } catch (error) {
      console.error('❌ [CacheService] 获取缓存聊天会话失败:', error)
      return []
    }
  }

  /**
   * 删除聊天缓存
   */
  async deleteChatCache(chatId: string): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      await dbManager.deleteChatMessages(chatId)
      await dbManager.deleteChatSession(chatId)
      console.log(`✅ [CacheService] 聊天缓存已删除: ${chatId}`)
    } catch (error) {
      console.error('❌ [CacheService] 删除聊天缓存失败:', error)
    }
  }

  /**
   * 同步内存数据到数据库
   */
  async syncToDatabase(
    memoryMessages: Map<string, Message[]>,
    memorySessions: Map<string, ChatSession>
  ): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      console.log('🔄 [CacheService] 开始同步数据到数据库...')

      // 同步消息
      for (const [chatId, messages] of memoryMessages.entries()) {
        if (messages.length > 0) {
          await this.cacheMessages(messages, chatId)
        }
      }

      // 同步会话
      for (const [_, session] of memorySessions.entries()) {
        await this.cacheChatSession(session)
      }

      console.log('✅ [CacheService] 数据同步完成')
    } catch (error) {
      console.error('❌ [CacheService] 数据同步失败:', error)
    }
  }

  /**
   * 从数据库加载数据到内存
   */
  async loadFromDatabase(): Promise<{
    messages: Map<string, Message[]>
    sessions: Map<string, ChatSession>
  }> {
    if (!this.isInitialized) {
      return {
        messages: new Map(),
        sessions: new Map()
      }
    }

    try {
      console.log('🔄 [CacheService] 从数据库加载数据...')

      // 加载会话
      const sessions = await this.getCachedChatSessions()
      const sessionMap = new Map<string, ChatSession>()

      for (const session of sessions) {
        sessionMap.set(session.userId, session)
      }

      // 加载消息（只加载最近的消息到内存）
      const messageMap = new Map<string, Message[]>()

      for (const session of sessions) {
        const messages = await this.getCachedMessages(session.userId, this.config.maxMemoryMessages)
        if (messages.length > 0) {
          messageMap.set(session.userId, messages)
        }
      }

      console.log(
        `✅ [CacheService] 数据加载完成 - 会话: ${sessions.length}, 消息组: ${messageMap.size}`
      )

      return {
        messages: messageMap,
        sessions: sessionMap
      }
    } catch (error) {
      console.error('❌ [CacheService] 数据加载失败:', error)
      return {
        messages: new Map(),
        sessions: new Map()
      }
    }
  }

  /**
   * 启动自动同步
   */
  private startAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }

    this.syncTimer = setInterval(() => {
      console.log('🔄 [CacheService] 执行自动同步...')
      // 这里可以触发同步逻辑，具体实现需要与 store 集成
    }, this.config.autoSyncInterval)
  }

  /**
   * 停止自动同步
   */
  private stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
    }
  }

  /**
   * 清理缓存
   */
  async clearCache(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      await dbManager.clearUserData()
      console.log('✅ [CacheService] 缓存已清理')
    } catch (error) {
      console.error('❌ [CacheService] 清理缓存失败:', error)
    }
  }

  /**
   * 关闭缓存服务
   */
  close(): void {
    this.stopAutoSync()
    dbManager.close()
    this.isInitialized = false
    console.log('✅ [CacheService] 缓存服务已关闭')
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{
    totalMessages: number
    totalSessions: number
    databaseSize: string
  }> {
    // 这里可以实现缓存统计逻辑
    return {
      totalMessages: 0,
      totalSessions: 0,
      databaseSize: '未知'
    }
  }
}

// 导出缓存服务实例
export const cacheService = new CacheService()
