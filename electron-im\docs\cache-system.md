# 历史消息缓存系统

本文档介绍了 Electron IM 应用中实现的基于 IndexedDB 的历史消息缓存系统。

## 功能特性

### 🔐 加密存储
- 使用 AES-GCM 256位加密算法
- 每个用户独立的加密密钥
- 消息内容完全加密，无法明文读取

### 💾 本地缓存
- 基于 IndexedDB 的浏览器本地存储
- 支持离线访问历史消息
- 自动同步内存和数据库数据

### 🚀 高性能
- 异步操作，不阻塞 UI
- 批量操作支持
- 智能缓存策略

### 👥 多用户支持
- 每个用户独立的数据空间
- 用户切换时自动清理和初始化

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Message Store │    │  Cache Service  │    │ Database Manager│
│   (Pinia)       │◄──►│                 │◄──►│   (IndexedDB)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        ▲                        ▲                        ▲
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI Components │    │  Auto Sync      │    │   Encryption    │
│                 │    │  Timer          │    │   (AES-GCM)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心组件

### 1. DatabaseManager (`db-manager.ts`)
负责 IndexedDB 的底层操作：
- 数据库初始化和版本管理
- 加密/解密操作
- 数据清理

### 2. CacheService (`cacheService.ts`)
提供高级缓存接口：
- 缓存策略管理
- 自动同步
- 批量操作
- 错误处理

### 3. Message Store 集成
在 Pinia store 中集成缓存：
- 自动缓存新消息
- 从缓存加载历史数据
- 同步内存和数据库状态

## 使用方法

### 初始化
```typescript
import { cacheService } from '@/services/cacheService'

// 在用户登录后初始化
await cacheService.initialize(userId)
```

### 缓存消息
```typescript
// 单条消息
await cacheService.cacheMessage(message, chatId)

// 批量消息
await cacheService.cacheMessages(messages, chatId)
```

### 读取缓存
```typescript
// 获取聊天消息
const messages = await cacheService.getCachedMessages(chatId, limit, offset)

// 获取最新消息
const latestMessage = await cacheService.getLatestCachedMessage(chatId)

// 获取聊天会话
const sessions = await cacheService.getCachedChatSessions()
```

### 清理缓存
```typescript
// 清理特定聊天
await cacheService.deleteChatCache(chatId)

// 清理所有缓存
await cacheService.clearCache()
```

## 数据结构

### 消息存储格式
```typescript
interface StoredMessage {
  id: string
  senderId: string
  receiverId: string
  content: EncryptedData  // 加密的消息内容
  timestamp: number
  type: number
  chatId: string
  userId: string         // 当前用户ID
}
```

### 会话存储格式
```typescript
interface StoredChatSession {
  userId: string         // 当前用户ID
  chatUserId: string     // 聊天对象用户ID
  userName: string
  userAvatar: string
  lastMessage?: StoredMessage
  unreadCount: number
  lastActiveTime: number
}
```

### 加密数据格式
```typescript
interface EncryptedData {
  data: string  // Base64 编码的加密数据
  iv: string    // Base64 编码的初始化向量
}
```

## 安全特性

### 加密算法
- **算法**: AES-GCM
- **密钥长度**: 256 位
- **初始化向量**: 每次加密生成随机 IV
- **认证**: GCM 模式提供内置的完整性验证

### 密钥管理
- 每个用户独立生成加密密钥
- 密钥存储在 IndexedDB 中
- 用户切换时重新生成密钥

### 数据隔离
- 每个用户的数据完全隔离
- 用户ID 作为数据分区标识
- 登出时清理所有相关数据

## 性能优化

### 缓存策略
- **内存优先**: 热数据保存在内存中
- **按需加载**: 只加载当前需要的数据
- **批量操作**: 减少数据库访问次数

### 自动同步
- 定时同步内存数据到数据库
- 异步操作，不阻塞用户界面
- 智能去重，避免重复存储

### 索引优化
- 按用户ID和聊天ID建立复合索引
- 按时间戳排序索引
- 快速查询和分页支持

## 测试

### 运行测试
```typescript
import { CacheTestUtils } from '@/utils/cacheTest'

// 运行所有测试
await CacheTestUtils.runAllTests()

// 性能测试
await CacheTestUtils.performanceTest()
```

### 浏览器控制台测试
在开发环境下，可以在浏览器控制台中运行：
```javascript
// 运行完整测试套件
await window.cacheTestUtils.runAllTests()

// 单独测试功能
await window.cacheTestUtils.testMessageCache()
await window.cacheTestUtils.testEncryption()
```
